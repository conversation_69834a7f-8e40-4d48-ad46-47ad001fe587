import React, { useState, useMemo } from 'react';
import { <PERSON> } from 'wouter';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ChevronDown, ChevronRight, Search, ExternalLink } from 'lucide-react';
import { useIndustries } from '@/hooks/use-industries';
import SEOHead from '@/components/seo-head';

interface PageCategory {
  title: string;
  description: string;
  count: number;
  pages: Array<{
    title: string;
    url: string;
    description?: string;
  }>;
}

const SiteMap: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const { data: industries, isLoading: industriesLoading } = useIndustries();

  // Static page categories
  const staticCategories: PageCategory[] = [
    {
      title: 'Best Startup Ideas',
      description: 'Curated collections of the most promising startup opportunities',
      count: 11,
      pages: [
        { title: 'Best Startup Ideas 2025', url: '/best/startup-ideas-2025', description: 'Top startup opportunities for 2025' },
        { title: 'AI Startup Ideas 2025', url: '/best/ai-startup-ideas-2025', description: 'AI-powered business opportunities' },
        { title: 'Profitable Business Ideas', url: '/best/profitable-business-ideas', description: 'High-profit potential business concepts' },
        { title: 'Online Business Opportunities', url: '/best/online-business-opportunities', description: 'Digital business ideas' },
        { title: 'SaaS Business Opportunities', url: '/best/saas-business-opportunities', description: 'Software-as-a-Service ideas' },
        { title: 'Financial Independence Ideas', url: '/best/financial-independence-ideas', description: 'Path to financial freedom' },
        { title: 'Family & Parenting Business Ideas', url: '/best/family-parenting-business-ideas', description: 'Family-focused business opportunities' },
        { title: 'Pet Care Startup Opportunities', url: '/best/pet-care-startup-opportunities', description: 'Pet industry business ideas' },
        { title: 'Startup Business Ideas', url: '/best/startup-business-ideas', description: 'General startup opportunities' },
        { title: 'EdTech Startup Ideas', url: '/best/edtech-startup-ideas', description: 'Education technology opportunities' },
        { title: 'Health & Fitness Tech Ideas', url: '/best/health-fitness-tech-ideas', description: 'Health technology business ideas' }
      ]
    },
    {
      title: 'FAQ & Guides',
      description: 'Comprehensive guides and frequently asked questions',
      count: 6,
      pages: [
        { title: 'How to Validate Startup Ideas', url: '/faq/how-to-validate-startup-ideas', description: 'Complete validation methodology' },
        { title: 'What Makes a Profitable Startup Idea', url: '/faq/what-makes-profitable-startup-idea', description: 'Key success factors' },
        { title: 'Reddit vs Traditional Market Research', url: '/faq/reddit-vs-traditional-market-research', description: 'Modern research methods' },
        { title: 'AI Tools for Entrepreneurs', url: '/faq/ai-tools-for-entrepreneurs', description: 'Essential AI tools guide' },
        { title: 'Startup Idea to MVP Process', url: '/faq/startup-idea-to-mvp-process', description: 'From concept to product' },
        { title: 'How to Find a Co-Founder', url: '/faq/how-to-find-co-founder', description: 'Co-founder matching guide' }
      ]
    },
    {
      title: 'Tool Comparisons',
      description: 'In-depth comparisons of popular business tools and platforms',
      count: 13,
      pages: [
        { title: 'ChatGPT vs AuraMind AI', url: '/alternatives/chatgpt-vs-auramind-ai', description: 'AI memory management comparison' },
        { title: 'Notion vs Competitors', url: '/alternatives/notion-vs-competitors', description: 'Productivity tool comparison' },
        { title: 'Traditional vs AI Tools', url: '/alternatives/traditional-vs-ai-tools', description: 'Modern tool evolution' },
        { title: 'Linktree vs Intelligent Alternatives', url: '/alternatives/linktree-vs-intelligent-alternatives', description: 'Link-in-bio solutions' },
        { title: 'Slack vs Discord for Business', url: '/alternatives/slack-vs-discord-business', description: 'Team communication tools' },
        { title: 'Zoom vs Google Meet', url: '/alternatives/zoom-vs-google-meet', description: 'Video conferencing comparison' },
        { title: 'Shopify vs WooCommerce', url: '/alternatives/shopify-vs-woocommerce', description: 'E-commerce platform comparison' },
        { title: 'Mailchimp vs ConvertKit', url: '/alternatives/mailchimp-vs-convertkit', description: 'Email marketing tools' },
        { title: 'Canva vs Figma', url: '/alternatives/canva-vs-figma', description: 'Design tool comparison' },
        { title: 'Airtable vs Notion Database', url: '/alternatives/airtable-vs-notion-database', description: 'Database solutions' },
        { title: 'HubSpot vs Pipedrive', url: '/alternatives/hubspot-vs-pipedrive', description: 'CRM comparison' },
        { title: 'Asana vs Monday.com', url: '/alternatives/asana-vs-monday', description: 'Project management tools' },
        { title: 'Stripe vs PayPal', url: '/alternatives/stripe-vs-paypal', description: 'Payment processing comparison' }
      ]
    },
    {
      title: 'Company Profiles',
      description: 'Detailed profiles of successful startups and companies',
      count: 4,
      pages: [
        { title: 'OpenAI Company Profile', url: '/company/openai', description: 'AI industry leader analysis' },
        { title: 'Stripe Company Profile', url: '/company/stripe', description: 'Payment processing giant' },
        { title: 'Notion Company Profile', url: '/company/notion', description: 'Productivity tool success story' },
        { title: 'Figma Company Profile', url: '/company/figma', description: 'Design collaboration platform' }
      ]
    },
    {
      title: 'Trends & Analysis',
      description: 'Market trends and startup opportunity analysis',
      count: 1,
      pages: [
        { title: 'Startup Trends 2025', url: '/trends/2025', description: 'Latest startup trends and opportunities' }
      ]
    }
  ];

  // Dynamic industry category
  const industryCategory: PageCategory = {
    title: 'Industry Insights',
    description: 'Deep-dive analysis of startup opportunities by industry',
    count: industries?.length || 35,
    pages: industries?.map(industry => ({
      title: industry.name,
      url: `/industry/${industry.slug}`,
      description: `${industry.ideaCount || 0} startup ideas in ${industry.name}`
    })) || []
  };

  const allCategories = [industryCategory, ...staticCategories];

  // Filter categories and pages based on search term
  const filteredCategories = useMemo(() => {
    if (!searchTerm.trim()) return allCategories;

    return allCategories.map(category => ({
      ...category,
      pages: category.pages.filter(page =>
        page.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        page.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    })).filter(category => category.pages.length > 0);
  }, [allCategories, searchTerm]);

  const toggleCategory = (categoryTitle: string) => {
    const newExpanded = new Set(expandedCategories);
    if (newExpanded.has(categoryTitle)) {
      newExpanded.delete(categoryTitle);
    } else {
      newExpanded.add(categoryTitle);
    }
    setExpandedCategories(newExpanded);
  };

  const totalPages = allCategories.reduce((sum, cat) => sum + cat.count, 0);

  return (
    <>
      <SEOHead
        title="Site Map | IdeaHunter - Complete Page Directory"
        description="Complete directory of all IdeaHunter pages including industry insights, startup ideas, FAQ guides, tool comparisons and market trends. Discover 600+ pages of startup opportunities."
        canonical="/sitemap"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebPage",
          "name": "IdeaHunter Site Map",
          "description": "Complete directory of all pages on IdeaHunter platform",
          "url": "https://ideahunter.today/sitemap",
          "mainEntity": {
            "@type": "SiteNavigationElement",
            "name": "Site Navigation",
            "url": "https://ideahunter.today/sitemap"
          }
        }}
      />

      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="text-center mb-8">
            <Link href="/">
              <Button variant="outline" className="mb-6">
                ← Back to Dashboard
              </Button>
            </Link>
            <h1 className="text-4xl font-bold text-white mb-4">
              Site Map
            </h1>
            <p className="text-xl text-gray-300 mb-6">
              Complete directory of all {totalPages}+ pages on IdeaHunter
            </p>
            
            {/* Search */}
            <div className="max-w-md mx-auto relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Search pages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-800 border-slate-700 text-white"
              />
            </div>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-8">
            {allCategories.map((category) => (
              <Card key={category.title} className="bg-slate-800 border-slate-700 p-4 text-center">
                <div className="text-2xl font-bold text-cyan-400">{category.count}</div>
                <div className="text-sm text-gray-300">{category.title}</div>
              </Card>
            ))}
          </div>

          {/* Categories */}
          <div className="space-y-6">
            {filteredCategories.map((category) => (
              <Card key={category.title} className="bg-slate-800 border-slate-700">
                <div className="p-6">
                  <div 
                    className="flex items-center justify-between cursor-pointer"
                    onClick={() => toggleCategory(category.title)}
                  >
                    <div className="flex items-center space-x-3">
                      {expandedCategories.has(category.title) ? (
                        <ChevronDown className="w-5 h-5 text-cyan-400" />
                      ) : (
                        <ChevronRight className="w-5 h-5 text-cyan-400" />
                      )}
                      <h2 className="text-xl font-semibold text-white">{category.title}</h2>
                      <Badge variant="secondary" className="bg-cyan-900 text-cyan-100">
                        {category.pages.length} pages
                      </Badge>
                    </div>
                  </div>
                  <p className="text-gray-300 mt-2 ml-8">{category.description}</p>
                  
                  {expandedCategories.has(category.title) && (
                    <div className="mt-4 ml-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {category.pages.map((page) => (
                        <Link key={page.url} href={page.url}>
                          <div className="p-3 bg-slate-700 rounded-lg hover:bg-slate-600 transition-colors group">
                            <div className="flex items-center justify-between">
                              <h3 className="font-medium text-white group-hover:text-cyan-400 transition-colors">
                                {page.title}
                              </h3>
                              <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-cyan-400 transition-colors" />
                            </div>
                            {page.description && (
                              <p className="text-sm text-gray-400 mt-1">{page.description}</p>
                            )}
                          </div>
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>

          {/* Footer CTA */}
          <div className="text-center mt-12">
            <Card className="bg-gradient-to-r from-cyan-900 to-purple-900 border-cyan-700 p-8">
              <h2 className="text-2xl font-bold text-white mb-4">
                Discover More Startup Opportunities
              </h2>
              <p className="text-gray-300 mb-6">
                Explore our AI-powered platform to find trending startup ideas from Reddit communities
              </p>
              <Link href="/">
                <Button className="bg-cyan-600 hover:bg-cyan-700 text-white">
                  Explore Ideas →
                </Button>
              </Link>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
};

export default SiteMap;
