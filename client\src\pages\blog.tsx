import React from 'react';
import { <PERSON> } from 'wouter';
import { ArrowLeft, Calendar, User, ArrowRight } from 'lucide-react';
import SEOHead from '../components/seo-head';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  author: string;
  date: string;
  readTime: string;
  category: string;
  slug: string;
}

const FEATURED_POSTS: BlogPost[] = [
  {
    id: '1',
    title: 'The Ultimate Guide to Validating Your Startup Idea in 2025',
    excerpt: 'Learn the proven framework used by successful entrepreneurs to validate startup ideas before investing time and money.',
    author: 'IdeaHunter Team',
    date: '2025-01-02',
    readTime: '8 min read',
    category: 'Validation',
    slug: 'ultimate-guide-validating-startup-idea-2025'
  },
  {
    id: '2',
    title: 'AI Startup Ideas That Are Actually Profitable in 2025',
    excerpt: 'Discover the most promising AI startup opportunities based on real market data and Reddit community insights.',
    author: 'IdeaHunter Team',
    date: '2025-01-01',
    readTime: '12 min read',
    category: 'AI Trends',
    slug: 'ai-startup-ideas-profitable-2025'
  },
  {
    id: '3',
    title: 'How We Analyze 10,000+ Reddit Posts to Find Hidden Startup Opportunities',
    excerpt: 'Behind the scenes look at our AI-powered process for discovering trending startup ideas from Reddit communities.',
    author: 'IdeaHunter Team',
    date: '2024-12-30',
    readTime: '6 min read',
    category: 'Platform',
    slug: 'how-we-analyze-reddit-posts-startup-opportunities'
  }
];

const RECENT_POSTS: BlogPost[] = [
  {
    id: '4',
    title: 'SaaS Startup Ideas with Low Competition and High Demand',
    excerpt: 'Explore underserved SaaS niches that offer great opportunities for new entrepreneurs.',
    author: 'IdeaHunter Team',
    date: '2024-12-28',
    readTime: '10 min read',
    category: 'SaaS',
    slug: 'saas-startup-ideas-low-competition-high-demand'
  },
  {
    id: '5',
    title: 'From Reddit Comment to Million-Dollar Startup: Success Stories',
    excerpt: 'Real examples of entrepreneurs who found their breakthrough ideas in Reddit discussions.',
    author: 'IdeaHunter Team',
    date: '2024-12-25',
    readTime: '7 min read',
    category: 'Success Stories',
    slug: 'reddit-comment-million-dollar-startup-success-stories'
  },
  {
    id: '6',
    title: 'The Psychology Behind Viral Startup Ideas on Reddit',
    excerpt: 'Understanding what makes certain startup ideas resonate with online communities.',
    author: 'IdeaHunter Team',
    date: '2024-12-22',
    readTime: '9 min read',
    category: 'Psychology',
    slug: 'psychology-viral-startup-ideas-reddit'
  }
];

export default function Blog() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <SEOHead
        title="Startup Ideas Blog | IdeaHunter"
        description="Discover insights, trends, and strategies for finding and validating startup ideas. Learn from real Reddit data and successful entrepreneurs."
        keywords={["startup blog", "business ideas", "entrepreneurship", "startup validation", "AI trends", "SaaS opportunities"]}
        url="https://ideahunter.today/blog"
      />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <Link href="/" className="inline-flex items-center text-cyan-400 hover:text-cyan-300 mb-6">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Link>
          
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">IdeaHunter Blog</h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Insights, trends, and strategies for discovering and validating startup opportunities
            </p>
          </div>
        </div>

        {/* Featured Posts */}
        <section className="mb-12">
          <h2 className="text-2xl font-bold mb-6">Featured Articles</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {FEATURED_POSTS.map((post) => (
              <article key={post.id} className="bg-gray-800 rounded-lg p-6 hover:bg-gray-750 transition-colors">
                <div className="mb-4">
                  <span className="inline-block bg-cyan-600 text-white text-xs px-2 py-1 rounded">
                    {post.category}
                  </span>
                </div>
                
                <h3 className="text-xl font-semibold mb-3 line-clamp-2">
                  <Link 
                    href={`/blog/${post.slug}`}
                    className="hover:text-cyan-400 transition-colors"
                  >
                    {post.title}
                  </Link>
                </h3>
                
                <p className="text-gray-300 mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
                
                <div className="flex items-center justify-between text-sm text-gray-400">
                  <div className="flex items-center">
                    <User className="w-4 h-4 mr-1" />
                    {post.author}
                  </div>
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    {new Date(post.date).toLocaleDateString()}
                  </div>
                </div>
                
                <div className="mt-4">
                  <Link 
                    href={`/blog/${post.slug}`}
                    className="inline-flex items-center text-cyan-400 hover:text-cyan-300 text-sm"
                  >
                    Read more <ArrowRight className="w-4 h-4 ml-1" />
                  </Link>
                </div>
              </article>
            ))}
          </div>
        </section>

        {/* Recent Posts */}
        <section>
          <h2 className="text-2xl font-bold mb-6">Recent Posts</h2>
          <div className="space-y-6">
            {RECENT_POSTS.map((post) => (
              <article key={post.id} className="bg-gray-800 rounded-lg p-6 hover:bg-gray-750 transition-colors">
                <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                  <div className="flex items-center space-x-4 mb-2 md:mb-0">
                    <span className="inline-block bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded">
                      {post.category}
                    </span>
                    <span className="text-sm text-gray-400">{post.readTime}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-400">
                    <Calendar className="w-4 h-4 mr-1" />
                    {new Date(post.date).toLocaleDateString()}
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold mb-3">
                  <Link 
                    href={`/blog/${post.slug}`}
                    className="hover:text-cyan-400 transition-colors"
                  >
                    {post.title}
                  </Link>
                </h3>
                
                <p className="text-gray-300 mb-4">
                  {post.excerpt}
                </p>
                
                <Link 
                  href={`/blog/${post.slug}`}
                  className="inline-flex items-center text-cyan-400 hover:text-cyan-300 text-sm"
                >
                  Read full article <ArrowRight className="w-4 h-4 ml-1" />
                </Link>
              </article>
            ))}
          </div>
        </section>

        {/* Newsletter CTA */}
        <section className="mt-12 bg-gradient-to-r from-cyan-600 to-blue-600 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Stay Updated</h2>
          <p className="text-lg mb-6">
            Get the latest startup insights and trending opportunities delivered to your inbox.
          </p>
          <div className="max-w-md mx-auto flex">
            <input 
              type="email" 
              placeholder="Enter your email"
              className="flex-1 px-4 py-2 rounded-l-lg text-gray-900"
            />
            <button className="bg-gray-900 text-white px-6 py-2 rounded-r-lg hover:bg-gray-800 transition-colors">
              Subscribe
            </button>
          </div>
        </section>
      </div>
    </div>
  );
}
