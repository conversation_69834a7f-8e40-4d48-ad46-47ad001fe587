import { createClient } from '@supabase/supabase-js';
import { writeFileSync } from 'fs';
import { join } from 'path';
import { config } from 'dotenv';
import type { Database } from '../shared/schema';

// Load environment variables
config();

// Supabase configuration
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please ensure VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are set in your .env file');
  process.exit(1);
}

const supabase = createClient<Database>(supabaseUrl, supabaseKey);

// Base URL for the site
const BASE_URL = 'https://ideahunter.today';

interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

function formatDate(date: Date): string {
  return date.toISOString().split('T')[0];
}

function generateSitemapXML(urls: SitemapUrl[]): string {
  const urlElements = urls.map(url => `
  <url>
    <loc>${url.loc}</loc>
    ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
    ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
    ${url.priority ? `<priority>${url.priority}</priority>` : ''}
  </url>`).join('');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlElements}
</urlset>`;
}

async function generateSitemap() {
  console.log('🚀 Generating sitemap...');
  
  const urls: SitemapUrl[] = [];
  const today = formatDate(new Date());

  // 1. Static pages
  const staticPages = [
    { path: '', priority: 1.0, changefreq: 'daily' as const },
    { path: '/dashboard', priority: 0.9, changefreq: 'daily' as const },
    { path: '/sitemap', priority: 0.9, changefreq: 'weekly' as const },
    { path: '/about', priority: 0.6, changefreq: 'monthly' as const },
    { path: '/privacy', priority: 0.3, changefreq: 'yearly' as const },
    { path: '/terms', priority: 0.3, changefreq: 'yearly' as const },
    { path: '/contact', priority: 0.5, changefreq: 'monthly' as const },
    { path: '/faq', priority: 0.7, changefreq: 'weekly' as const },
  ];

  staticPages.forEach(page => {
    urls.push({
      loc: `${BASE_URL}${page.path}`,
      lastmod: today,
      changefreq: page.changefreq,
      priority: page.priority
    });
  });

  // 2. Industry pages
  console.log('📊 Fetching industries...');
  const { data: industries, error: industriesError } = await supabase
    .from('industries')
    .select('id, name')
    .order('name');

  if (industriesError) {
    console.error('Error fetching industries:', industriesError);
  } else if (industries) {
    industries.forEach(industry => {
      const slug = createSlug(industry.name);
      urls.push({
        loc: `${BASE_URL}/industry/${slug}`,
        lastmod: today,
        changefreq: 'weekly',
        priority: 0.8
      });
    });
    console.log(`✅ Added ${industries.length} industry pages`);
  }

  // 3. Startup idea pages
  console.log('💡 Fetching startup ideas...');
  const { data: ideas, error: ideasError } = await supabase
    .from('startup_ideas')
    .select('id, title, created_at')
    .order('created_at', { ascending: false });

  if (ideasError) {
    console.error('Error fetching ideas:', ideasError);
  } else if (ideas) {
    ideas.forEach(idea => {
      const slug = createSlug(idea.title);
      const lastmod = idea.created_at ? formatDate(new Date(idea.created_at)) : today;
      
      urls.push({
        loc: `${BASE_URL}/idea/${idea.id}/${slug}`,
        lastmod,
        changefreq: 'monthly',
        priority: 0.7
      });
    });
    console.log(`✅ Added ${ideas.length} idea pages`);
  }

  // 4. Best/Category pages (programmatic SEO pages)
  const categoryPages = [
    'startup-ideas-2025',
    'ai-startup-ideas-2025',
    'profitable-business-ideas',
    'online-business-opportunities',
    'saas-business-opportunities',
    'financial-independence-ideas',
    'family-parenting-business-ideas',
    'pet-care-startup-opportunities',
    'startup-business-ideas',
    'edtech-startup-ideas',
    'health-fitness-tech-ideas'
  ];

  categoryPages.forEach(category => {
    urls.push({
      loc: `${BASE_URL}/best/${category}`,
      lastmod: today,
      changefreq: 'weekly',
      priority: 0.8
    });
  });

  // 5. FAQ topic pages
  const faqTopics = [
    'how-to-validate-startup-ideas',
    'what-makes-profitable-startup-idea',
    'reddit-vs-traditional-market-research',
    'ai-tools-for-entrepreneurs',
    'startup-idea-to-mvp-process',
    'how-to-find-co-founder'
  ];

  faqTopics.forEach(topic => {
    urls.push({
      loc: `${BASE_URL}/faq/${topic}`,
      lastmod: today,
      changefreq: 'monthly',
      priority: 0.6
    });
  });

  // 6. Alternative comparison pages
  const alternativePages = [
    'chatgpt-vs-auramind-ai',
    'notion-vs-competitors',
    'traditional-vs-ai-tools',
    'linktree-vs-intelligent-alternatives',
    'slack-vs-discord-business',
    'zoom-vs-google-meet',
    'shopify-vs-woocommerce',
    'mailchimp-vs-convertkit',
    'canva-vs-figma',
    'airtable-vs-notion-database',
    'hubspot-vs-pipedrive',
    'asana-vs-monday',
    'stripe-vs-paypal'
  ];

  alternativePages.forEach(comparison => {
    urls.push({
      loc: `${BASE_URL}/alternatives/${comparison}`,
      lastmod: today,
      changefreq: 'monthly',
      priority: 0.7
    });
  });

  // 7. Trends pages
  const currentYear = new Date().getFullYear();
  const trendPages = [
    `${currentYear}`
  ];

  trendPages.forEach(period => {
    urls.push({
      loc: `${BASE_URL}/trends/${period}`,
      lastmod: today,
      changefreq: 'weekly',
      priority: 0.7
    });
  });

  // Generate XML
  const sitemapXML = generateSitemapXML(urls);
  
  // Write to file
  const outputPath = join(process.cwd(), 'client', 'public', 'sitemap.xml');
  writeFileSync(outputPath, sitemapXML, 'utf-8');
  
  console.log(`✅ Sitemap generated successfully!`);
  console.log(`📍 Location: ${outputPath}`);
  console.log(`📊 Total URLs: ${urls.length}`);
  console.log(`🔗 Sitemap URL: ${BASE_URL}/sitemap.xml`);
  
  return {
    totalUrls: urls.length,
    staticPages: staticPages.length,
    industryPages: industries?.length || 0,
    ideaPages: ideas?.length || 0,
    categoryPages: categoryPages.length,
    faqPages: faqTopics.length,
    alternativePages: alternativePages.length,
    trendPages: trendPages.length
  };
}

// Run the script
generateSitemap()
  .then(stats => {
    console.log('\n📈 Sitemap Statistics:');
    console.log(`- Static pages: ${stats.staticPages}`);
    console.log(`- Industry pages: ${stats.industryPages}`);
    console.log(`- Idea pages: ${stats.ideaPages}`);
    console.log(`- Category pages: ${stats.categoryPages}`);
    console.log(`- FAQ pages: ${stats.faqPages}`);
    console.log(`- Alternative pages: ${stats.alternativePages}`);
    console.log(`- Trend pages: ${stats.trendPages}`);
    console.log(`- Total URLs: ${stats.totalUrls}`);
  })
  .catch(error => {
    console.error('❌ Error generating sitemap:', error);
    process.exit(1);
  });

export { generateSitemap };
